package com.thedasagroup.suminative.ui.reservations

import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest
import com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class ReservationsViewModel @AssistedInject constructor(
    @Assisted state: ReservationsState,
    private val getActiveReservationsUseCase: GetActiveReservationsUseCase,
    private val getAllReservationsUseCase: GetAllReservationsUseCase,
    private val createReservationUseCase: CreateReservationUseCase,
    private val editReservationUseCase: EditReservationUseCase,
    private val cancelReservationUseCase: CancelReservationUseCase,
    private val getReservationAreasUseCase: GetReservationAreasUseCase,
    private val getReservationTablesUseCase: GetReservationTablesUseCase
) : MavericksViewModel<ReservationsState>(state) {

    fun loadActiveReservations() {
        setState { copy(activeReservationsResponse = Loading()) }
       viewModelScope.launch {
           getActiveReservationsUseCase().execute {
               when (it) {
                   is Success -> {
                       copy(activeReservationsResponse = it())
                   }
                   else -> {
                       copy(activeReservationsResponse = Uninitialized)
                   }
               }
           }
       }
    }

    fun loadAllReservations() {
        setState { copy(allReservationsResponse = Loading()) }
        viewModelScope.launch {
            getAllReservationsUseCase().execute {
                when (it) {
                    is Success -> {
                        copy(allReservationsResponse = it())
                    }
                    else -> {
                        copy(allReservationsResponse = Uninitialized)
                    }
                }
            }
        }
    }

    fun createReservation(request: CreateReservationRequest) {
        setState { copy(createReservationResponse = Loading()) }
        viewModelScope.launch {
            createReservationUseCase(request).execute {
                when (it) {
                    is Success -> {
                        // Refresh reservations after successful creation
                        loadActiveReservations()
                        loadAllReservations()
                        copy(createReservationResponse = it())
                    }
                    else -> {
                        copy(createReservationResponse = Uninitialized)
                    }
                }
            }
        }
    }

    fun editReservation(reservationId: Int, request: EditReservationRequest) {
        setState { copy(editReservationResponse = Loading()) }
        viewModelScope.launch {
            editReservationUseCase(reservationId, request).execute {
                when (it) {
                    is Success -> {
                        // Refresh reservations after successful edit
                        loadActiveReservations()
                        loadAllReservations()
                        copy(editReservationResponse = it())
                    }
                    else -> {
                        copy(editReservationResponse = Uninitialized)
                    }
                }
            }
        }
    }

    fun cancelReservation(reservationId: Int) {
        setState { copy(cancelReservationResponse = Loading()) }
        viewModelScope.launch {
            cancelReservationUseCase(reservationId).execute {
                when (it) {
                    is Success -> {
                        // Refresh reservations after successful cancellation
                        loadActiveReservations()
                        loadAllReservations()
                        copy(cancelReservationResponse = it())
                    }
                    else -> {
                        copy(cancelReservationResponse = Uninitialized)
                    }
                }
            }
        }
    }

    fun setSelectedTab(index: Int) {
        setState { copy(selectedTabIndex = index) }
    }

    fun refresh() {
        setState { copy(isRefreshing = true) }
        viewModelScope.launch {
            try {
                // Load both active and all reservations concurrently
                val activeJob = async {
                    getActiveReservationsUseCase().execute { result ->
                        when (result) {
                            is Success -> copy(activeReservationsResponse = result())
                            else -> copy(activeReservationsResponse = Uninitialized)
                        }
                    }
                }
                val allJob = async {
                    getAllReservationsUseCase().execute { result ->
                        when (result) {
                            is Success -> copy(allReservationsResponse = result())
                            else -> copy(allReservationsResponse = Uninitialized)
                        }
                    }
                }

                // Wait for both to complete
                activeJob.await()
                allJob.await()
            } finally {
                setState { copy(isRefreshing = false) }
            }
        }
    }

    fun showEditDialog(reservation: com.thedasagroup.suminative.data.model.response.reservations.Reservation) {
        val editData = EditReservationData(
            reservationId = reservation.id ?: -1,
            customerName = reservation.customerName ?: "",
            customerPhone = reservation.customerPhone ?: "",
            tableName = reservation.tableName ?: "",
            reservationTime = reservation.reservationTime ?: "",
            numPeople = reservation.numPeople ?: 0,
            reservationStatus = reservation.reservationStatus ?: 0
        )
        setState {
            copy(
                showEditDialog = true,
                editingReservation = editData
            )
        }
    }

    fun hideEditDialog() {
        setState {
            copy(
                showEditDialog = false,
                editingReservation = null
            )
        }
    }

    /**
     * Load reservation areas for the current store
     */
    fun loadReservationAreas() {
        setState { copy(areasResponse = Loading()) }
        viewModelScope.launch {
            getReservationAreasUseCase().execute {
                when (it) {
                    is Success -> {
                        copy(areasResponse = it())
                    }
                    else -> {
                        copy(areasResponse = Uninitialized)
                    }
                }
            }
        }
    }

    /**
     * Load tables for a specific area
     * @param areaId The area ID to load tables for
     */
    fun loadReservationTables(areaId: Int) {
        setState {
            copy(
                tablesResponse = Loading(),
                selectedAreaId = areaId
            )
        }
        viewModelScope.launch {
            getReservationTablesUseCase(areaId).execute {
                when (it) {
                    is Success -> {
                        copy(tablesResponse = it())
                    }
                    else -> {
                        copy(tablesResponse = Uninitialized)
                    }
                }
            }
        }
    }

    /**
     * Clear selected area and tables
     */
    fun clearAreaSelection() {
        setState {
            copy(
                selectedAreaId = null,
                tablesResponse = Uninitialized
            )
        }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<ReservationsViewModel, ReservationsState> {
        override fun create(state: ReservationsState): ReservationsViewModel
    }

    companion object :
        com.airbnb.mvrx.MavericksViewModelFactory<ReservationsViewModel, ReservationsState> by hiltMavericksViewModelFactory()
}
